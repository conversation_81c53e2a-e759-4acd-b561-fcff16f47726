package com.sankuai.wallemonitor.risk.center.domain.service;


import com.sankuai.wallemonitor.risk.center.infra.dto.upgrade.AlertUpgradeStrategyDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskAlertRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DxCardParamVTO;

/**
 * 风险告警升级处理器
 */
public interface AlertUpgradeProcessor {

    /**
     * 处理单个升级记录
     * @param record 原始告警记录
     * @param strategy 升级策略
     * @return 是否处理成功
     */
    boolean processUpgradeRecord(RiskAlertRecordDO record,AlertUpgradeStrategyDTO strategy);
   

    /**
     * 创建升级记录
     * @param originalRecord 原始告警记录
     * @param upgradeMessageId 升级消息ID
     */
    void createUpgradeRecord(AlertUpgradeStrategyDTO strategy, DxCardParamVTO dxCardParam, String outBizId,
            RiskAlertRecordDO originalRecord, String upgradeMessageId);
}
