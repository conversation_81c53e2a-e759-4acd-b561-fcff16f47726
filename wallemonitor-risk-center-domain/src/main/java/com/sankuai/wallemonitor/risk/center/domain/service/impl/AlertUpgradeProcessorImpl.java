package com.sankuai.wallemonitor.risk.center.domain.service.impl;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.base.Joiner;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.wallemonitor.risk.center.domain.component.AlertTemplateBuilder;
import com.sankuai.wallemonitor.risk.center.domain.service.AlertUpgradeProcessor;
import com.sankuai.wallemonitor.risk.center.domain.strategy.aggregate.AggregateAlertContext;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.DxNoticeAdapter;
import com.sankuai.wallemonitor.risk.center.infra.dto.upgrade.AlertUpgradeStrategyDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.AlertRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.UpgradeStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskAlertRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.UserInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.VehicleInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskAlertRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseVehicleRelationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiderCaseVehicleRelationDOParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockKeyPreUtil;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DxCardParamVTO;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 风险告警升级处理器实现
 */
@Slf4j
@Service
public class AlertUpgradeProcessorImpl implements AlertUpgradeProcessor {

    @Resource
    private RiskAlertRecordRepository riskAlertRecordRepository;


    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private DxNoticeAdapter dxNoticeAdapter;

    @Resource
    private AlertTemplateBuilder alertTemplateBuilder;

    @Resource
    private RiskCaseVehicleRelationRepository riskCaseVehicleRelationRepository;

    @Resource
    private VehicleInfoRepository vehicleInfoRepository;



    @Resource
    private UserInfoRepository userInfoRepository;

    @Resource
    private com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository idGenerateRepository;

    @Resource
    private LockUtils lockUtils;


    /**
     * 处理单个升级记录
    */
    @Override
    public boolean processUpgradeRecord(RiskAlertRecordDO record,AlertUpgradeStrategyDTO strategy) {
        try {
            log.info("开始处理升级记录, recordId: {}, messageId: {}", record.getId(), record.getMessageId());

            
            // 1. 解析原记录的事件ID列表;
            if (StringUtils.isBlank(record.getEventIds())) {
                return false;
            }
            // 取第一个
            String eventId = record.getEventIds().split(",")[0];
        
            // 2. 查询风险事件详情
            if (StringUtils.isBlank(eventId)) {
                return false;
            }

            List<RiskCaseDO> riskCaseList = Stream.of(eventId)
                    .map(riskCaseRepository::getByCaseId) // 使用caseId查询，因为eventIds实际存储的是caseId
                    .filter(java.util.Objects::nonNull)
                    .collect(Collectors.toList()); 
    
            if (CollectionUtils.isEmpty(riskCaseList)) {
                return false;
            }
            if (CollectionUtils.isEmpty(riskCaseList)) {
                log.error("未找到对应的风险事件, eventIds: {}", record.getEventIds());
                return false;
            }

            // 4. 创建聚合上下文
            AggregateAlertContext context = buildAggregateAlertContext(riskCaseList);

            // 5. 复用现有的AlertTemplateBuilder构建升级卡片模板
            DxCardTemplateDO upgradeTemplate = alertTemplateBuilder.buildUpgradeTemplate(
                    strategy,
                    riskCaseList,
                    context
            );
            if (upgradeTemplate == null) {
                log.error("构建升级卡片模板失败, recordId: {}", record.getId());
                return false;
            }
            // 生成唯一的业务ID - 使用通用业务ID生成方法
            String outBizId = idGenerateRepository.generateBusinessId(IDBizEnum.ALERT_UPGRADE_BIZ_ID,
                                                         strategy.getAlertPolicy(),System.currentTimeMillis());

            // 查询uidList
            List<String> masterMisList = strategy.getAlertTemplate().getMasterName();
            // 3. MIS -> UID
            List<Long> uidList = userInfoRepository.batchGetUidsByMis(masterMisList);

            DxCardParamVTO dxCardParam = DxCardParamVTO.builder()
                    .templateId(Long.valueOf(strategy.getAlertTemplate().getTemplateId()))
                    .groupIdList(strategy.getAlertTemplate().getGroupIdList())
                    .arguments(upgradeTemplate.toArguments())
                    .abstractText(upgradeTemplate.summarize())
                    .outBizId(outBizId)
                    .version(System.currentTimeMillis())
                    .uidList(uidList)
                    .build();

            // 2. 发送升级卡片
            String upgradeMessageId = dxNoticeAdapter.sendDxCard(dxCardParam);
            if (StringUtils.isBlank(upgradeMessageId)) {
                log.error("发送升级卡片失败, recordId: {}", record.getId());
                return false;
            }

            // 3. 创建升级记录
            createUpgradeRecord(strategy,dxCardParam,outBizId,record, upgradeMessageId);

            // 4. 更新原记录的升级状态
            updateOriginalRecordUpgradeStatus(record, upgradeMessageId);

            return true;

        } catch (Exception e) {
            log.error("处理升级记录异常, recordId: {}", record.getId(), e);
            return false;
        }
    }


    /**
     * 创建升级记录
     */
    @Override
    public void createUpgradeRecord(AlertUpgradeStrategyDTO strategy, DxCardParamVTO dxCardParam, String outBizId,
            RiskAlertRecordDO originalRecord, String upgradeMessageId) {
        try {
            String eventIds = originalRecord.getEventIds();
            RiskAlertRecordDO upgradeRecord = RiskAlertRecordDO.builder()
                    .alertPolicy(strategy.getAlertPolicy())
                    .configName(strategy.getConfigName())
                    .bizId(outBizId)
                    .messageId(upgradeMessageId)
                    .eventIds(eventIds)
                    .groupId(Joiner.on(",").join(dxCardParam.getGroupIdList()))
                    .arguments(dxCardParam.getArguments())
                    .status(AlertRecordStatusEnum.UNPROCESSED)
                    .upgradeStatus(UpgradeStatusEnum.NO_NEED_UPGRADE)
                    .createTime(new Date())
                    .updateTime(new Date())
                    .build();

            riskAlertRecordRepository.save(upgradeRecord);
            log.info("升级记录创建成功, upgradeRecordId: {}, upgradeMessageId: {}", 
                    upgradeRecord.getId(), upgradeMessageId);

        } catch (Exception e) {
            log.error("创建升级记录异常, originalRecordId: {}, upgradeMessageId: {}", 
                    originalRecord.getId(), upgradeMessageId, e);
        }
    }
 
 

    /**
     * 更新原记录的升级状态（带分布式锁保护）
     */
    private void updateOriginalRecordUpgradeStatus(RiskAlertRecordDO record, String upgradeMessageId) {
        // 构建告警记录专用锁键
        Set<String> lockKeys = LockKeyPreUtil.buildAlertRecordKeys(record.getId().toString());

        try {
            // 使用分布式锁保护更新操作
            lockUtils.batchLockNoWait(lockKeys, () -> {
                // 重新查询最新记录状态，避免并发更新冲突
                RiskAlertRecordDO latestRecord = riskAlertRecordRepository.getById(record.getId());
                if (latestRecord == null) {
                    log.error("记录不存在, recordId: {}", record.getId());
                    return;
                }

                // 检查是否已经被升级，避免重复升级
                if (UpgradeStatusEnum.UPGRADED.equals(latestRecord.getUpgradeStatus())) {
                    log.info("记录已经被升级, recordId: {}, 跳过更新", record.getId());
                    return;
                }

                // 更新升级状态
                latestRecord.setUpgradeStatus(UpgradeStatusEnum.UPGRADED);
                latestRecord.setUpgradeMessageId(upgradeMessageId);
                latestRecord.setUpdateTime(new Date());
                riskAlertRecordRepository.save(latestRecord);

                log.info("原记录升级状态更新成功, recordId: {}, upgradeMessageId: {}",
                        latestRecord.getId(), upgradeMessageId);
            });
        } catch (Exception e) {
            log.error("获取分布式锁失败或更新操作异常, recordId: {}", record.getId(), e);
        }
    }

    /**
     * 构建聚合告警上下文
     *
     * @param riskCaseList 风险事件列表
     * @return 聚合告警上下文
     */
    private AggregateAlertContext buildAggregateAlertContext(List<RiskCaseDO> riskCaseList) {
        List<String> caseIdList = riskCaseList.stream().map(RiskCaseDO::getCaseId).collect(Collectors.toList());
        Map<String, String> caseId2VinMap = riskCaseVehicleRelationRepository.queryByParam(
                        RiderCaseVehicleRelationDOParamDTO.builder().caseIdList(caseIdList).build()).stream()
                .collect(Collectors.toMap(RiskCaseVehicleRelationDO::getCaseId, RiskCaseVehicleRelationDO::getVin,
                        (o1, o2) -> o1));
        Map<String, VehicleInfoDO> vehicleInfoMap = vehicleInfoRepository.queryByVinList(
                        Lists.newArrayList(caseId2VinMap.values())).stream()
                .collect(Collectors.toMap(VehicleInfoDO::getVin, Function.identity(), (o1, o2) -> o1));
        Map<String, VehicleInfoDO> caseId2VehicleInfoMap = caseId2VinMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> vehicleInfoMap.get(entry.getValue())));

        AggregateAlertContext context = new AggregateAlertContext();
        context.updateCaseId2VehicleInfoMap(caseId2VehicleInfoMap);
        return context;
    }

}
