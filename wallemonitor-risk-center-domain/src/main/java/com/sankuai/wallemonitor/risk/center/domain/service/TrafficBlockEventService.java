package com.sankuai.wallemonitor.risk.center.domain.service;

import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficBlockEventMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;

/**
 * 堵路事件服务接口
 * 负责处理堵路事件的核心业务逻辑
 */
public interface TrafficBlockEventService {

    /**
     * 处理堵路事件消息
     * 根据vin匹配5分钟内最近的停滞不当事件，如果匹配到则记录相关停滞信息并创建堵路事件
     *
     * @param message 堵路事件消息
     * @param caseId 停滞不当事件ID
     * @param vin 车架号
     *
     */
    void processTrafficBlockEvent(TrafficBlockEventMessageDTO message, String caseId, String vin);

    /**
     * 根据vin匹配5分钟内最近的停滞不当事件
     *
     * @param vin 车架号
     * @param occurTime 堵路事件发生时间（毫秒时间戳）
     * @return 匹配到的停滞不当事件ID
     */
    RiskCaseDO findRecentStrandingCaseByVin(String vin, Long occurTime);


}
