package com.sankuai.wallemonitor.risk.center.domain.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.xframe.config.annotation.ConfigValue;
import com.sankuai.walledelivery.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.param.RiskCaseUpdatedParamDTO;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskCaseOperateService;
import com.sankuai.wallemonitor.risk.center.domain.service.TrafficBlockEventService;
import com.sankuai.wallemonitor.risk.center.infra.constant.LionKeyConstant;
import com.sankuai.wallemonitor.risk.center.infra.convert.RiskCaseMessageDTOConvert.VehicleEventDataMessageExtInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficBlockEventMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficBlockEventSendDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskCaseRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.RiskCaseDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DateTimeTemplateConstant;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 堵路事件服务实现类
 */
@Service
@Slf4j
public class TrafficBlockEventServiceImpl implements TrafficBlockEventService {

    @Resource
    private RiskCaseRepository riskCaseRepository;

    @Resource
    private RiskCaseOperateService riskCaseOperateService;


    @ConfigValue(key = LionKeyConstant.TRAFFIC_BLOCK_EVENT_PROCESS_TIME_THRESHOLD, defaultValue = "{}")
    private Integer vehicleStagnationProcessTimeThreshold;
    /**
     * 处理堵路事件消息
     * @param message 堵路事件消息
     */
    @Override
    public void processTrafficBlockEvent(TrafficBlockEventMessageDTO message, String caseId, String vin) {

        // 转换时间格式
        String triggerTimeStr = message.getData().getTriggerTime();
        Date triggerDate = DatetimeUtil.parseDate(triggerTimeStr,
                DateTimeTemplateConstant.YEAR_MONTH_DAY_HOUR_MIN_SECOND_MIRCO);
        long triggerTimeLong = DatetimeUtil.getTimeStamp(triggerDate);
        RiskCaseDO recentStrandingCase = findRecentStrandingCaseByVin(vin,
                triggerTimeLong);
        if (Objects.isNull(recentStrandingCase)) {
            return;
        }

        // 将查询到的RiskCaseDO转换为发送用的DTO
        TrafficBlockEventSendDTO sendRequestDTO = convertToSendRequest(recentStrandingCase, message, vin);


        VehicleEventDataMessageExtInfoDTO extInfoDTO = new VehicleEventDataMessageExtInfoDTO();
        Map<String, Object> contentMap = JacksonUtils.from(JacksonUtils.to(sendRequestDTO),
                new TypeReference<Map<String, Object>>() {
                });
        // 可以添加更多的相关信息
        extInfoDTO.setContent(contentMap);

        // 从消息中提取数据，转换为参数DTO
        RiskCaseUpdatedParamDTO paramDTO = RiskCaseUpdatedParamDTO.builder()
                .eventId(caseId)
                .caseId(caseId)
                .vinList(Collections.singletonList(vin))
                .timestamp(triggerTimeLong)
                .recallTime(triggerTimeLong)
                .messageExtInfo(extInfoDTO)
                .source(RiskCaseSourceEnum.CLOUD_CURSOR)
                .type(RiskCaseTypeEnum.TRAFFIC_BLOCK_EVENT)
                .status(RiskCaseStatusEnum.NO_DISPOSAL)
                .build();

        // 调用已有的处理方法
        riskCaseOperateService.createOrUpdateRiskCase(paramDTO);
    }

    /**
     * 根据vin匹配5分钟内最近的停滞不当事件 匹配逻辑：根据创建时间（createTime）在当前时间前后5分钟范围内的记录
     *
     * @param vin  车架号
     * @param time
     * @return 匹配到的停滞不当事件ID
     */
    @Override
    public RiskCaseDO findRecentStrandingCaseByVin(String vin, Long time) {

        long startTime = time - vehicleStagnationProcessTimeThreshold * 60 * 1000;
        try {
            // 构造查询参数，使用createTime时间范围
            RiskCaseDOQueryParamDTO queryParamDTO = RiskCaseDOQueryParamDTO.builder()
                    .vinList(Collections.singletonList(vin))
                    .caseTypeList(Collections.singletonList(RiskCaseTypeEnum.VEHICLE_STAND_STILL.getCode()))
                    .source(RiskCaseSourceEnum.BEACON_TOWER.getCode())
                    .createTimeCreateTo(new Date(startTime)) // 创建时间大于startTime
                    .createTimeBelowTo(new Date(time))
                    .leftJoinRelation(true)
                    .build();

            // 查询该时间窗口内该vin的停滞不当事件
            List<RiskCaseDO> riskCaseList = riskCaseRepository.queryByParam(queryParamDTO);
            if (riskCaseList == null || riskCaseList.isEmpty()) {
                log.info("未查询到vin: {} 在创建时间窗口内的停滞不当事件", vin);
                return null;
            }
            // 找出创建时间最接近的那条记录
            return riskCaseList.stream()
                    .min(Comparator.comparingLong(riskCase ->
                            Math.abs(riskCase.getCreateTime().getTime() - time)))
                    .orElse(null);
        } catch (Exception e) {
            log.error("查询停滞不当事件异常，vin: {}, 创建时间窗口: {} - {}", vin, new Date(startTime), new Date(System.currentTimeMillis()), e);
        }
        return null;
    }

    /**
     * 将RiskCaseDO转换为发送用的DTO
     *
     * @param riskCaseDO 风险案例
     * @param message   堵路事件消息
     * @return TrafficBlockEventSendRequestDTO
     */
    private TrafficBlockEventSendDTO convertToSendRequest(RiskCaseDO riskCaseDO, TrafficBlockEventMessageDTO message,
            String vin) {
        if (riskCaseDO == null) {
            return null;
        }

        // 获取位置信息
        Double latitude = null;
        Double longitude = null;
        PositionDO position = riskCaseDO.getLocation();
        if (position != null) {
            latitude = position.getLatitude();
            longitude = position.getLongitude();
        }

        // 构建内层DTO
        TrafficBlockEventSendDTO sendDTO = TrafficBlockEventSendDTO.builder()
                .caseId(riskCaseDO.getCaseId())
                .vin(vin)
                .occurTime(DatetimeUtil.formatTime(riskCaseDO.getCreateTime()))
                .closeTime(DatetimeUtil.formatTime(riskCaseDO.getCloseTime()))
                .latitude(latitude)
                .longitude(longitude)
                .extInfo(new HashMap<>())
                .position(TrafficBlockEventSendDTO.fromMessagePosition(
                        message.getData().getPosition()
                ))
                .reporterInfo(TrafficBlockEventSendDTO.fromMessageReporterInfo(
                        message.getData().getReporterInfo()
                ))
                .build();

        return sendDTO;
    }

        
 
}



