package com.sankuai.wallemonitor.risk.center.server.consumer;

import com.alibaba.druid.util.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.walleeve.utils.CheckUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.service.TrafficBlockEventService;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.dto.TrafficBlockEventMessageDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.IDBizEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.exception.ParamInputErrorException;
import com.sankuai.wallemonitor.risk.center.infra.exception.UnableGetLockException;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.IDGenerateRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DateTimeTemplateConstant;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Collections;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;



/**
 * 堵路事件消费者
 * 负责消费堵路事件消息并进行业务处理
 */
@Component
@Slf4j
public class TrafficBlockEventConsumer {

    @Resource
    private TrafficBlockEventService trafficBlockEventService;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private LockUtils lockUtils;
    
    @Resource
    private IDGenerateRepository idGenerateRepository;

    //堵路事件常量
    private static final String TRAFFIC_BLOCK_EVENT_TYPE = "堵路事件";

    private static final String TRAFFIC_BLOCK_EVENT_OPERATION_TYPE = "mender_event_report";


    /**
     * 消费堵路事件接受消息 topic: mad_teleop_cockpit_operations
     */
    @MafkaConsumer(
            namespace = "waimai",
            topic = "mad_teleop_cockpit_operations",
            group = "wallemonitor.risk.teleop.cockpit.operations.consumer",
            deadLetter = true,
            deadLetterDelayMills = 6 * 1000
    )
    @OperateEnter(OperateEnterActionEnum.RISK_EVENT_CONSUMER_ENTER_TRAFFIC_BLOCK)
    public ConsumeStatus receive(String msg) {
        try {
            // 1. 解析消息
            TrafficBlockEventMessageDTO message = JacksonUtils.from(msg,
                    new TypeReference<TrafficBlockEventMessageDTO>() {
                    });

            // 2. 校验消息基本结构
            CheckUtil.isNotNull(message, "消费堵路事件消息为空");

            CheckUtil.isNotNull(message.getData(), "消费堵路事件消息data为空");

            // 3. 获取并校验VIN
            String vin = message.getData().getVin().toUpperCase();
            CheckUtil.isNotNull(vin, "消费堵路事件消息vin为空");

            // 判断是不是堵路事件
            if (!StringUtils.equals(message.getData().getType(), TRAFFIC_BLOCK_EVENT_TYPE)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            if (!StringUtils.equals(message.getOperationType(), TRAFFIC_BLOCK_EVENT_OPERATION_TYPE)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            // 时间转换
            String timestamp = message.getData().getTriggerTime();
            Long triggerTime = DatetimeUtil.getTimeStamp(DatetimeUtil.parseDate(timestamp,
                    DateTimeTemplateConstant.YEAR_MONTH_DAY_HOUR_MIN_SECOND_MIRCO));
            // 4. 生成caseId
            String caseId = idGenerateRepository.generateByKey(
                    IDBizEnum.RISK_CASE_ID,
                    Collections.singletonList(vin),
                    RiskCaseSourceEnum.CLOUD_CURSOR,
                    RiskCaseTypeEnum.TRAFFIC_BLOCK_EVENT,
                    triggerTime
            );
            trafficBlockEventService.processTrafficBlockEvent(message, caseId, vin);
            return ConsumeStatus.CONSUME_SUCCESS;

        } catch (UnableGetLockException e) {
            log.warn("消费堵路事件消息锁冲突失败, msg={}", msg, e);
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (ParamInputErrorException e) {
            log.warn("消费堵路事件消息业务参数错误, msg={}", msg, e);
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Throwable e) {
            log.error("消费堵路事件消息处理异常, msg={}", msg, e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
    }

}
