package com.sankuai.wallemonitor.risk.center.server.aop.dto;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import java.io.IOException;
import org.locationtech.jts.geom.Point;
import org.springframework.stereotype.Component;

@Component
public class PointSerializer extends JsonSerializer<Point> {

    @Override
    public void serialize(Point point, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.writeStartObject();
        gen.writeNumberField("x", point.getX());
        gen.writeNumberField("y", point.getY());
        gen.writeEndObject();
    }
}
