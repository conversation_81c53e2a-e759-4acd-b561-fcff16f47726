package com.sankuai.wallemonitor.risk.center.server.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.meituan.xframe.boot.zebra.autoconfigure.router.annotation.ZebraForceMaster;
import com.sankuai.wallemonitor.risk.center.domain.service.AlertUpgradeProcessor;
import com.sankuai.wallemonitor.risk.center.infra.annotation.OperateEnter;
import com.sankuai.wallemonitor.risk.center.infra.constant.CraneConstant;
import com.sankuai.wallemonitor.risk.center.infra.dto.upgrade.AlertUpgradeConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.upgrade.AlertUpgradeStrategyDTO;
import com.sankuai.wallemonitor.risk.center.infra.enums.AlertRecordStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskAlertRecordDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.RiskAlertRecordRepository;
import com.sankuai.wallemonitor.risk.center.infra.utils.lock.LockUtils;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 风险告警升级定时任务
 * 每分钟执行一次，检查需要升级的告警记录并发送升级卡片
 */
@Slf4j
@CraneConfiguration
public class AlertUpgradeCrane {

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private LockUtils lockUtils;

    @Resource
    private RiskAlertRecordRepository riskAlertRecordRepository;


    @Resource
    private AlertUpgradeProcessor alertUpgradeProcessor;

    @Crane(CraneConstant.ALERT_UPGRADE_CRANE)
    @OperateEnter(OperateEnterActionEnum.ALERT_UPGRADE_CRANE)
    @ZebraForceMaster
    public void run() throws Exception {
        log.info("风险告警升级定时任务开始执行");
        // 1. 检查配置是否启用
        AlertUpgradeConfigDTO config = lionConfigRepository.getAlertUpgradeConfig();
        if (config == null || !config.isValid()) {
            log.info("风险告警升级配置未启用或无效，跳过执行");
            return;
        }
        // 2. 获取有效的升级策略
        List<AlertUpgradeStrategyDTO> validStrategies = config.getValidStrategies();
        if (CollectionUtils.isEmpty(validStrategies)) {
            return;
        }

        // 3. 先查询所有待升级记录
        List<RiskAlertRecordDO> allPendingRecords = getAllPendingUpgradeRecords();
        if (CollectionUtils.isEmpty(allPendingRecords)) {
            log.info("没有待升级记录");
            return;
        }

        // 4. 处理每个策略
        for (AlertUpgradeStrategyDTO strategy : validStrategies) {
            processUpgradeStrategyWithRecords(strategy, allPendingRecords);
        }
        log.info("风险告警升级检查执行完成");

    }


    /**
     * 获取所有待升级记录
     */
    private List<RiskAlertRecordDO> getAllPendingUpgradeRecords() {
        // 1. 获取所有需要升级的记录
        List<RiskAlertRecordDO> allUpgradeRecords = riskAlertRecordRepository.getPendingUpgradeRecords();

        if (CollectionUtils.isEmpty(allUpgradeRecords)) {
            return Collections.emptyList();
        }

        // 2. 按message_id分组，每组取最新记录（按create_time降序）
        Map<String, List<RiskAlertRecordDO>> recordsByMessageId = allUpgradeRecords.stream()
                .filter(record -> record != null && StringUtils.isNotBlank(record.getMessageId()))
                .collect(Collectors.groupingBy(RiskAlertRecordDO::getMessageId));

        // 3. 从每组中选择创建时间最新的记录
        List<RiskAlertRecordDO> latestRecords = recordsByMessageId.values().stream()
                .map(records -> records.stream().max(Comparator.comparing(RiskAlertRecordDO::getCreateTime,
                                Comparator.nullsLast(Comparator.naturalOrder())))
                        .orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        log.info("获取所有待升级记录：总记录数 {}, 分组后记录数 {}", allUpgradeRecords.size(), latestRecords.size());

        return latestRecords;
    }

    /**
     * 处理单个升级策略（使用已查询的记录）
     */
    private void processUpgradeStrategyWithRecords(AlertUpgradeStrategyDTO strategy,
            List<RiskAlertRecordDO> allPendingRecords) {
        try {
            log.info("开始处理升级策略: {}, caseType: {}", strategy.getConfigName(), strategy.getCaseType());

            // 1. 计算时间阈值：当前时间减去配置的升级阈值分钟数
            Date thresholdTime = DatetimeUtil.getBeforeTime(new Date(), TimeUnit.MINUTES,
                    strategy.getAlertTemplate().getUpgradeThreshold());

            // 2. 过滤符合当前策略的记录
            List<RiskAlertRecordDO> matchingRecords = allPendingRecords.stream()
                    // 过滤策略匹配
                    .filter(record -> Objects.equals(strategy.getAlertPolicy(), record.getAlertPolicy())
                            || Objects.equals(strategy.getConfigName(), record.getConfigName()))
                    // 过滤升级条件
                    .filter(record -> record.getCreateTime().before(thresholdTime)
                            && record.getStatus() == AlertRecordStatusEnum.UNPROCESSED)
                    .collect(java.util.stream.Collectors.toList());
            if (CollectionUtils.isEmpty(matchingRecords)) {
                log.info("策略 {} 没有匹配的待升级记录", strategy.getConfigName());
                return;
            }

            log.info("策略 {} 找到 {} 条待升级记录", strategy.getConfigName(), matchingRecords.size());

            // 3. 处理每条记录
            int successCount = 0;
            int failCount = 0;

            for (RiskAlertRecordDO record : matchingRecords) {
                try {
                    boolean success = alertUpgradeProcessor.processUpgradeRecord(record, strategy);
                    if (success) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    log.error("处理升级记录失败, recordId: {}", record.getId(), e);
                    failCount++;
                }
            }

            log.info("策略 {} 处理完成, 成功: {}, 失败: {}", strategy.getConfigName(), successCount, failCount);

        } catch (Exception e) {
            log.error("处理升级策略异常: {}", strategy.getConfigName(), e);
        }
    }



}
