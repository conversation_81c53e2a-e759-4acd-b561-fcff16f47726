package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.wallecmdb.data.eve.replay.inquire.api.thrift.response.VehicleDataInfoVO;
import com.sankuai.walleeve.utils.DatetimeUtil;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.process.VehicleLocationUpdatedProcess;
import com.sankuai.wallemonitor.risk.center.domain.result.ISWaitingInQueueResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckActionContext;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl.*;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.HdMapAdapter;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.VehicleAdapter;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.RiskAutoCheckConfigDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleInQueuePositionDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.VehicleObstacleInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.lion.PreHandleDataConfig;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.onboardmessage.PerceptionObstacleDTO.PerceptionObstacle.Position;
import com.sankuai.wallemonitor.risk.center.infra.enums.DriverModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.TrafficLightTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.DomainEventFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.common.GeoQueryDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.LionConfigRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoContextRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.VehicleRuntimeInfoLocationRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.dbrepo.dto.VehicleRuntimeLocationDOQueryParamDTO;
import com.sankuai.wallemonitor.risk.center.infra.utils.geo.GeoToolsUtil;
import com.sankuai.wallemonitor.risk.center.infra.vto.result.VehicleEveInfoVTO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import lombok.*;
import org.apache.commons.io.FileUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.io.ClassPathResource;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

public class ISPreHandleDataTest extends SpringTestBase {

    @MockBean
    private VehicleAdapter vehicleAdapter;

    @Resource
    private HdMapAdapter hdMapAdapter;

    @Resource
    private VehicleRuntimeInfoContextRepository contextRepository;

    @Resource
    private ISWaitingInQueueV8 isInQueue;

    @Resource
    private ISSwitchPower isSwitchPower;

    @Resource
    private LionConfigRepository lionConfigRepository;

    @Resource
    private VehicleRuntimeInfoLocationRepository locationRepository;

    @Resource
    private VehicleLocationUpdatedProcess vehicleLocationUpdatedProcess;

    @Resource
    private ISPreHandleDataActionV2 preHandleDataActionV2;

    @Resource
    private ISWaitingInQueueV8 v6;

    private ISCheckActionContext buildPreHandleDataContext(VehicleRuntimeInfoContextDO contextDO) {
        // 获取预处理数据配置
        PreHandleDataConfig preHandleDataConfig = lionConfigRepository.getPreHandleDataConfig();

        // 构建动作配置映射
        Map<String, Map<String, Object>> actionConfigMap = new HashMap<>();
        actionConfigMap.put("ISPreHandleDataAction", JacksonUtils.fromMap(JacksonUtils.to(preHandleDataConfig)));

        // 构建并返回ISCheckActionContext
        return ISCheckActionContext.builder()
                // 设置队列项目信息
                .item(RiskCheckingQueueItemDO.builder()
                        .occurTime(new Date())
                        .vin(contextDO.getVin())
                        .build())
                // 设置车辆运行时上下文
                .vehicleRunTimeContext(contextDO)
                // 设置当前动作名称
                .currentActionName("ISPreHandleDataAction")
                // 设置动作配置映射
                .actionConfigMap(actionConfigMap)
                .build();
    }

    @Test
    public void testOppositeDirectionObstacles() {
        System.out.println("=== 测试：对向车道障碍物检测 ===");
        setupVehicleAdapterMock();
        VehicleRuntimeInfoContextDO contextDO = prepareOppositeDirectionTestContext();

        // 更新缓存和数据库
        contextRepository.updateCache(contextDO, contextDO.getLastUpdateTime().getTime());
        contextRepository.save(contextDO);

        // 构建测试上下文
        ISCheckActionContext context = buildPreHandleDataContext(contextDO);

        System.out.println("预处理前");
        System.out.println("可借道车道: " + contextDO.getUsableLaneIds());
        System.out.println("可借道车道数量: " + (contextDO.getUsableLaneIds() != null ? contextDO.getUsableLaneIds().size() : 0));

        // 执行预处理
        preHandleDataActionV2.execute(context);

        // 输出测试结果
        System.out.println("预处理后");
        System.out.println("车辆位置: [" + contextDO.getLng() + ", " + contextDO.getLat() + "]");
        System.out.println("车辆速度: " + contextDO.getSpeed() + " m/s");
        System.out.println("当前车道: " + contextDO.getVehicleCurLaneId());
        System.out.println("周围车道: " + contextDO.getVehicleAroundLaneId());
        System.out.println("可借道车道: " + contextDO.getUsableLaneIds());
        System.out.println("可借道车道数量: " + (contextDO.getUsableLaneIds() != null ? contextDO.getUsableLaneIds().size() : 0));


        // 继续执行排队检测
        executeQueueDetection(context);
    }

    private VehicleRuntimeInfoContextDO prepareOppositeDirectionTestContext() {
        VehicleRuntimeInfoContextDO contextDO = new VehicleRuntimeInfoContextDO();
        contextDO.setVin("LMTZSV022NC048391");

        // 设置车辆基本信息 - 基于提供的数据
        contextDO.setDriveMode(DriverModeEnum.AUTONOMOUS_DRIVING);
        contextDO.setSpeed(0.0); // 车辆静止
        contextDO.setLat("22.713085");
        contextDO.setLng("114.030172");
        contextDO.setDistanceToJunction(85.4);
        contextDO.setLastUpdateTime(new Date());

        // 设置车道信息
        contextDO.setVehicleCurLaneId("lane_c15dd13cb4dd");
        contextDO.setVehicleCurLaneIdList(Lists.newArrayList("lane_c15dd13cb4dd"));
        contextDO.setUsableLaneIds(Lists.newArrayList("lane_fd33a1b9a9b0"));

        // 创建包含对向车道障碍物的感知数据
        String perceptionObstacleDTOStr = createOppositeDirectionObstaclesData();
        PerceptionObstacleDTO perceptionObstacleDTO = JacksonUtils.from(perceptionObstacleDTOStr, PerceptionObstacleDTO.class);

        // 设置多个障碍物位置，模拟真实场景
        setupOppositeDirectionObstacles(perceptionObstacleDTO);

        contextDO.updateObstacle(perceptionObstacleDTO, 100.0);

        return contextDO;
    }

    private String createOppositeDirectionObstaclesData() {
        return "{\n" +
                "  \"arbitrationMonitorContext\": {\n" +
                "    \"noiseCategory\": \"NOISE_CATEGORY_LOW\"\n" +
                "  },\n" +
                "  \"errorCode\": \"OK\",\n" +
                "  \"perceptionObstacle\": [\n" +
                "    {\n" +
                "      \"acceleration\": {\"x\": 0, \"y\": 0, \"z\": 0},\n" +
                "      \"confidence\": 1,\n" +
                "      \"direction\": {\"x\": 0.9646002998761698, \"y\": 0.26371625182911973, \"z\": 0},\n" +
                "      \"height\": 3.046875,\n" +
                "      \"id\": \"2774531\",\n" +
                "      \"length\": 7.798828125,\n" +
                "      \"obstacleType\": {\"coarseType\": \"CAR\", \"fineType\": \"FINE_TRUCK\"},\n" +
                "      \"position\": {\"x\": 194926.2125694892, \"y\": 2514818.1429327326, \"z\": 53.97206693944488},\n" +
                "      \"theta\": 0.26687281943575664,\n" +
                "      \"type\": \"VEHICLE\",\n" +
                "      \"velocity\": {\"x\": 0, \"y\": 0, \"z\": 0},\n" +
                "      \"width\": 2.796875\n" +
                "    },\n" +
                "    {\n" +
                "      \"acceleration\": {\"x\": 0, \"y\": 0, \"z\": 0},\n" +
                "      \"confidence\": 1,\n" +
                "      \"direction\": {\"x\": -0.9578515932128525, \"y\": -0.2872635120922631, \"z\": 0},\n" +
                "      \"height\": 1.8017578125,\n" +
                "      \"id\": \"2780976\",\n" +
                "      \"length\": 4.6904296875,\n" +
                "      \"obstacleType\": {\"coarseType\": \"CAR\", \"fineType\": \"FINE_CAR\"},\n" +
                "      \"position\": {\"x\": 194909.6318974358, \"y\": 2514816.721948718, \"z\": 54.073672690510165},\n" +
                "      \"theta\": -2.8502239465785704,\n" +
                "      \"type\": \"VEHICLE\",\n" +
                "      \"width\": 2.123046875\n" +
                "    },\n" +
                "    {\n" +
                "      \"acceleration\": {\"x\": 0, \"y\": 0, \"z\": 0},\n" +
                "      \"confidence\": 1,\n" +
                "      \"direction\": {\"x\": -0.9801661758281824, \"y\": -0.19817736440456887, \"z\": 0},\n" +
                "      \"height\": 2.073974609375,\n" +
                "      \"id\": \"2774823\",\n" +
                "      \"length\": 5.197265625,\n" +
                "      \"obstacleType\": {\"coarseType\": \"CAR\", \"fineType\": \"FINE_CAR\"},\n" +
                "      \"position\": {\"x\": 194926.0845288327, \"y\": 2514813.3374120956, \"z\": 53.69661276158454},\n" +
                "      \"theta\": -2.9420946003667523,\n" +
                "      \"type\": \"VEHICLE\",\n" +
                "      \"velocity\": {\"x\": 0, \"y\": 0, \"z\": 0},\n" +
                "      \"width\": 2.1640625\n" +
                "    },\n" +
                "    {\n" +
                "      \"acceleration\": {\"x\": 0, \"y\": 0, \"z\": 0},\n" +
                "      \"confidence\": 1,\n" +
                "      \"direction\": {\"x\": -0.9776647373647674, \"y\": -0.21017055291708156, \"z\": 0},\n" +
                "      \"height\": 1.7236328125,\n" +
                "      \"id\": \"2775688\",\n" +
                "      \"length\": 0.611572265625,\n" +
                "      \"obstacleType\": {},\n" +
                "      \"position\": {\"x\": 194929.30000000002, \"y\": 2514815.5, \"z\": 53.46464701918213},\n" +
                "      \"theta\": -2.929843247719846,\n" +
                "      \"type\": \"PEDESTRIAN\",\n" +
                "      \"width\": 0.64501953125\n" +
                "    }\n" +
                "  ],\n" +
                "  \"pose\": {}\n" +
                "}";
    }

    private void setupOppositeDirectionObstacles(PerceptionObstacleDTO perceptionObstacleDTO) {
        // 基于真实数据设置障碍物位置
        List<PerceptionObstacleDTO.PerceptionObstacle> obstacles = perceptionObstacleDTO.getPerceptionObstacle();

        // 设置第一个障碍物：前方同向大货车
        PositionDO truck1Position = GeoToolsUtil.wgs84ToUtm(114.0303142, 22.7131271);
        obstacles.get(0).setPosition(Position.builder()
                .x(truck1Position.getLongitude()).y(truck1Position.getLatitude()).build());

        // 设置第二个障碍物：左侧对向小车
        PositionDO oppositeCar1Position = GeoToolsUtil.wgs84ToUtm(114.0301533, 22.7131113);
        obstacles.get(1).setPosition(Position.builder()
                .x(oppositeCar1Position.getLongitude()).y(oppositeCar1Position.getLatitude()).build());

        // 设置第三个障碍物：远处对向车辆
        PositionDO oppositeCar2Position = GeoToolsUtil.wgs84ToUtm(114.0303139, 22.7130837);
        obstacles.get(2).setPosition(Position.builder()
                .x(oppositeCar2Position.getLongitude()).y(oppositeCar2Position.getLatitude()).build());

        // 设置第四个障碍物：对向行人
        PositionDO pedestrianPosition = GeoToolsUtil.wgs84ToUtm(114.0303447, 22.7131038);
        obstacles.get(3).setPosition(Position.builder()
                .x(pedestrianPosition.getLongitude()).y(pedestrianPosition.getLatitude()).build());

        System.out.println("设置障碍物位置:");
        System.out.println("- 前方货车: [114.0303142, 22.7131271]");
        System.out.println("- 左侧对向车辆: [114.0301533, 22.7131113]");
        System.out.println("- 远处对向车辆: [114.0303139, 22.7130837]");
        System.out.println("- 对向行人: [114.0303447, 22.7131038]");
    }


    private void executeQueueDetection(ISCheckActionContext context) {
        System.out.println("\n=== 执行排队检测 ===");

        RiskAutoCheckConfigDTO riskAutoMarkConfigDTO = lionConfigRepository.getRiskAutoMarkConfigByVersion("");
        context.setCurrentActionName("ISWaitingInQueueV6");
        riskAutoMarkConfigDTO.getActionCommonConfig()
                .put("ISWaitingInQueueV6", riskAutoMarkConfigDTO.getActionCommonConfig().get("ISWaitingInQueueV5"));
        context.setActionConfigMap(riskAutoMarkConfigDTO.getActionCommonConfig());

        ISCheckActionResult queueResult = v6.execute(context);
        System.out.println("排队检测结果: " + JacksonUtils.to(queueResult.getActionResult()));
        System.out.println("排队检测状态: " + queueResult.getCategoryEnum());
        System.out.println("========================");
    }

    private void setupVehicleAdapterMock() {
        // 创建模拟的车辆信息返回对象
        VehicleEveInfoVTO mockVehicleInfo = VehicleEveInfoVTO.builder()
                .vin("LMTZSV022NC048391")
                .build();

        // 设置 Mock 行为
        Mockito.when(vehicleAdapter.queryRuntimeVehicleInfoByVin(Mockito.anyString()))
                .thenReturn(mockVehicleInfo);
    }





}