package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.infra.adaptar.client.DxNoticeAdapter;
import com.sankuai.wallemonitor.risk.center.infra.model.dx.DxCardTemplateDO;
import com.sankuai.wallemonitor.risk.center.infra.vto.param.DxCardParamVTO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * 大象通知适配器测试类
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Slf4j
public class DxNoticeAdapterTest extends SpringTestBase {

    @Resource
    private DxNoticeAdapter dxNoticeAdapter;

    /**
     * 测试发送大象卡片功能
     * 使用真实的模版ID和群ID进行测试
     */
    @Test
    public void testSendDxCard() {
        // 1. 构建卡片模板数据（使用提供的具体数据）
        DxCardTemplateDO dxCardTemplate = DxCardTemplateDO.builder()
                // 基础字段
                .title("h2启动启动事件")
                .themeColor("green")
                
                // 数据字段
                .occurTime("2025-06-05 13:33:42 ~ 2025-06-05 16:33:42")
                .vehicleNameV2("[h24-045|www.baidu.com]")
                .securityOfficer("[@赵端财|mtdaxiang://www.meituan.com/profile?uid=2205492755&isAt=true]")
                .prompt("车辆准备，请尽快上车。")
                .occurTimeImg("https://walle.meituan.com/replay/video/occurTime?vin=LA71AUB1XS0501819&view=front&occurTime=20250605185909") 
                .feedback("like")
                
                // 显示控制字段
                .showOccurTime(true)
                .showVehicleNameV2(true)
                .showSecurityOfficer(true)
                .showPrompt(true)
                .showOccurTimeImg(true)
                .showFeedBack(true)
                .showButton(true)
                
                // 按钮状态
                .isBtnDisabled(false)
                .build();

        // 2. 构建 DxCardParamVTO 参数
        DxCardParamVTO dxCardParam = DxCardParamVTO.builder()
                .templateId(22823L)  // 模版ID
                .groupIdList(Arrays.asList(64014518267L))  // 群ID
                .arguments(dxCardTemplate.toArguments())  // 将模板对象转换为JSON字符串
                .abstractText(dxCardTemplate.summarize())  // 摘要信息
                .outBizId("tes-" + System.currentTimeMillis())  // 外部唯一ID
                .version(System.currentTimeMillis()+1000)  // 版本号
                .uidList(Arrays.asList(2205492755L)) // 扩展字段
                .build();
        
        // 3. 打印请求参数用于调试
        log.info("发送卡片请求参数: {}", JacksonUtils.to(dxCardParam));
        log.info("卡片模板参数: {}", dxCardTemplate.toArguments());

        // 4. 调用发送方法
        try {
            String messageId = dxNoticeAdapter.sendDxCard(dxCardParam);
            log.info("发送卡片消息结果: messageId={}", messageId);
            
            // 5. 验证结果
            if (messageId != null && !messageId.isEmpty()) {
                log.info("✅ 卡片发送成功! MessageId: {}", messageId);
                System.out.println("✅ 测试通过 - 卡片发送成功! MessageId: " + messageId);
            } else {
                log.warn("⚠️ 卡片发送失败，返回的messageId为空");
                System.out.println("⚠️ 测试警告 - 卡片发送失败，返回的messageId为空");
            }
        } catch (Exception e) {
            log.error("❌ 发送卡片异常", e);
            System.out.println("❌ 测试失败 - 发送卡片异常: " + e.getMessage());
        }
    }

 
}
