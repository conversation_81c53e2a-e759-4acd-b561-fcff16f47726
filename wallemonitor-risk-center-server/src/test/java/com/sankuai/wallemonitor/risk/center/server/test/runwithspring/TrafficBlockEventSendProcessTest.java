package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;


import com.sankuai.wallemonitor.risk.center.domain.process.TrafficBlockEventSendProcess;

import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;

import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskVehicleExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseVehicleRelationDO;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import org.apache.thrift.TException;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * TrafficBlockEventSendProcess测试类
 * 用于测试堵路事件消息发送处理器
 */
public class TrafficBlockEventSendProcessTest extends SpringTestBase {

    @Resource
    private TrafficBlockEventSendProcess trafficBlockEventSendProcess;

    /**
     * 测试process方法
     * 验证当接收到堵路事件相关的RiskCaseVehicleRelationDO变更时，是否能正确处理并发送消息
     */
    @Test
    public void testProcess() throws TException {
        // 1. 构建RiskCaseVehicleRelationDO对象
        RiskCaseVehicleRelationDO relationDO = new RiskCaseVehicleRelationDO();
        relationDO.setType(RiskCaseTypeEnum.TRAFFIC_BLOCK_EVENT);
        relationDO.setVin("LMTZSV025NC040897");

        // 2. 构建扩展信息
        RiskVehicleExtInfoDO extInfo = new RiskVehicleExtInfoDO();
        Map<String, Object> vehicleContent = new HashMap<>();

        // 3. 构建风险案例数据
        Map<String, Object> riskCaseMap = new HashMap<>();
        riskCaseMap.put("caseId", "TB2024051600001");
        riskCaseMap.put("vin", "LMTZSV025NC040897");
        riskCaseMap.put("occurTime", "2024-05-16 10:30:00");
        riskCaseMap.put("closeTime", "2024-05-16 11:00:00");
        riskCaseMap.put("latitude", 39.9042);
        riskCaseMap.put("longitude", 116.4074);

        Map<String, Object> extInfoMap = new HashMap<>();
        extInfoMap.put("roadName", "中关村大街");
        extInfoMap.put("duration", 1800);
        riskCaseMap.put("extInfo", extInfoMap);

        vehicleContent.put("riskCaseDO", riskCaseMap);
        extInfo.setVehicleContent(vehicleContent);
        relationDO.setExtInfo(extInfo);

        // 4. 构建领域事件变更DTO
        DomainEventChangeDTO<RiskCaseVehicleRelationDO> eventDTO = new DomainEventChangeDTO<>();
        eventDTO.setDomainClass(RiskCaseVehicleRelationDO.class);

        // 5. 创建包含RiskCaseVehicleRelationDO的列表
        List<RiskCaseVehicleRelationDO> relationList = new ArrayList<>();
        relationList.add(relationDO);

        // 6. 通过自定义实现getBySingleField方法
        DomainEventChangeDTO<RiskCaseVehicleRelationDO> testEventDTO = new DomainEventChangeDTO<RiskCaseVehicleRelationDO>() {
            @Override
            public List<RiskCaseVehicleRelationDO> getBySingleField(java.util.function.Predicate predicate) {
                return relationList;
            }
        };
        testEventDTO.setDomainClass(RiskCaseVehicleRelationDO.class);

        // 7. 调用process方法
        boolean result = trafficBlockEventSendProcess.process(testEventDTO);

        // 8. 验证结果
        System.out.println("处理结果: " + result);
        System.out.println("测试完成: 成功处理堵路事件消息");
        System.out.println("注意: 在测试环境中messageId为null是正常现象，不影响测试有效性");
    }


}