package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.wallemonitor.risk.center.domain.service.TrafficBlockEventService;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.server.consumer.TrafficBlockEventConsumer;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import javax.annotation.Resource;
import org.junit.Test;

public class TrafficBlockEventServiceTest extends SpringTestBase {

    @Resource
    private TrafficBlockEventService trafficBlockEventService;

    @Resource
    private TrafficBlockEventConsumer trafficBlockEventConsumer;

    @Test
    public void testFindRecentStrandingCaseByVin() {
        // 准备测试数据
        String testVin = "LMTZSV025NC040897"; // 使用之前测试用的VIN

        long currentTimeMillis = DatetimeUtil.convertDatetimeStr2Date("2025-04-18 15:48:19").getTime();

        // 调用方法进行测试
        RiskCaseDO result = trafficBlockEventService.findRecentStrandingCaseByVin(testVin, currentTimeMillis);

        // 验证结果
        System.out.println("找到的RiskCaseDO: " + result);

        // 如果找到了记录，验证记录的属性
        if (result != null) {
            System.out.println("找到匹配的记录:");
            System.out.println("案例ID: " + result.getCaseId());
            System.out.println("事件类型: " + result.getType());
            System.out.println("创建时间: " + result.getCreateTime());
            System.out.println("时间差(ms): " + Math.abs(result.getCreateTime().getTime() - currentTimeMillis));
        } else {
            System.out.println("未找到匹配的记录，请确认数据库中是否存在该VIN对应的停滞不当事件记录");
        }
    }

    // 堵路事件测试消息 - 参考RiskCaseEventConsumerTest的格式
    private String message = "{\n"
            + "    \"operationType\": \"mender_event_report\",\n"
            + "    \"data\": {\n"
            + "        \"vin\": \"lmtzsv027mc042469\",\n"
            + "        \"mapName\": \"tangshanceshichang_admap\",\n"
            + "        \"position\": {\n"
            + "            \"x\": 669836.203277033,\n"
            + "            \"y\": 4342488.771113178\n"
            + "        },\n"
            + "        \"reporterInfo\": {\n"
            + "            \"cockpitId\": \"cock-00870bb680b022e00a179935a4b6f7ed\",\n"
            + "            \"cockpitName\": \"wj-assist-12\",\n"
            + "            \"reporter\": \"zongkaili\"\n"
            + "        },\n"
            + "        \"source\": \"cockpit\",\n"
            + "        \"triggerTime\": \"2025-06-09 11:43:08.196\",\n"
            + "        \"type\": \"堵路事件\"\n"
            + "    }\n"
            + "}";

    /**
     * 测试TrafficBlockEventConsumer的receive方法
     * 基于您提供的实际堵路事件消息进行测试
     */
    @Test
    public void testReceiveTrafficBlockEventMessage() {
        System.out.println("=== 开始测试TrafficBlockEventConsumer.receive方法 ===");

        System.out.println("测试消息内容:");
        System.out.println(message);
        System.out.println();

        // 2. 调用consumer的receive方法
        System.out.println("调用TrafficBlockEventConsumer.receive()方法...");
        ConsumeStatus result = trafficBlockEventConsumer.receive(message);

        // 3. 验证结果
        System.out.println("=== 测试结果 ===");
        System.out.println("消费状态: " + result);
        System.out.println("预期状态: " + ConsumeStatus.CONSUME_SUCCESS);

        // 4. 输出处理流程说明
        System.out.println();
        System.out.println("=== 处理流程说明 ===");
        System.out.println("1. 消息解析: 将JSON字符串解析为TrafficBlockEventMessageDTO对象");
        System.out.println("2. 消息校验: 验证消息结构、VIN、事件类型等");
        System.out.println("3. VIN转换: lmtzsv027mc042469 → LMTZSV027MC042469");
        System.out.println("4. 时间转换: 2025-06-09 11:43:08.196 → 时间戳");
        System.out.println("5. 生成CaseID: 基于VIN、时间戳等生成堵路事件ID");
        System.out.println("6. 业务处理: 调用TrafficBlockEventService.processTrafficBlockEvent()");
        System.out.println("   - 查找5分钟内的停滞不当事件");
        System.out.println("   - 如果找到匹配事件，创建堵路事件记录");
        System.out.println("   - 发送MQ消息到下游系统");

        System.out.println();
        System.out.println("=== 测试完成 ===");
        if (result == ConsumeStatus.CONSUME_SUCCESS) {
            System.out.println("✅ 测试成功: 消息消费正常");
        } else {
            System.out.println("❌ 测试失败: 消息消费异常，状态=" + result);
        }
    }

}
