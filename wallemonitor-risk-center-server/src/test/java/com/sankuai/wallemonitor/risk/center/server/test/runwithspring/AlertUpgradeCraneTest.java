package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.wallemonitor.risk.center.server.crane.AlertUpgradeCrane;
import org.junit.Test;
import javax.annotation.Resource;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
/**
 * 风险告警升级定时任务测试
 */
public class AlertUpgradeCraneTest extends SpringTestBase {
    @Resource
    private AlertUpgradeCrane alertUpgradeCrane;
    
    /**
     * 测试完整的成功流程 - 集成测试
     */
    @Test
    public void testRun_IntegrationSuccess() throws Exception {

        try {
            alertUpgradeCrane.run();
            // 如果没有抛出异常，说明整体流程正常
            System.out.println("AlertUpgradeCrane 整体逻辑执行成功");
        } catch (Exception e) {
            // 记录异常信息，但不失败测试（因为可能依赖外部服务）
            System.err.println("AlertUpgradeCrane 执行异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

}
