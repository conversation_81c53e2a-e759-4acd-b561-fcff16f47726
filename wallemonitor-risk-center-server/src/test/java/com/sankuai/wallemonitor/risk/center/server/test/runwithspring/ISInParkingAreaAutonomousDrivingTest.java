package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.result.ISInParkingAreaResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.ISCheckActionResult;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.ISCheckActionContext;
import com.sankuai.wallemonitor.risk.center.domain.strategy.action.impl.ISInParkingArea;
import com.sankuai.wallemonitor.risk.center.infra.enums.DriverModeEnum;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCheckingQueueItemDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleRuntimeInfoContextDO;
import com.sankuai.wallemonitor.risk.center.infra.utils.time.DatetimeUtil;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.Date;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * 自动驾驶状态下停车区域检查逻辑测试
 */
public class ISInParkingAreaAutonomousDrivingTest extends SpringTestBase {

    @Resource
    private ISInParkingArea isInParkingArea;

    @Test
    public void testAutonomousDrivingInParkingArea() {
        // 测试场景：自动驾驶状态下，车辆在停车区域内
        System.out.println("=== 测试自动驾驶状态下的停车区域检查逻辑 ===");
        
        // 构建自动驾驶状态的车辆上下文 - 使用你JSON数据中的经纬度
        VehicleRuntimeInfoContextDO vehicleContext = VehicleRuntimeInfoContextDO.builder()
                .vin("LA71AUB10S0508648")
                .driveMode(DriverModeEnum.AUTONOMOUS_DRIVING)
                .speed(0.0)
                .lng("116.552205")  // 你提供的经纬度
                .lat("40.095842")
                .lastUpdateTime(new Date())
                .build();

        // 构建风险检查队列项
        RiskCheckingQueueItemDO itemDO = RiskCheckingQueueItemDO.builder()
                .tmpCaseId("TEST_AUTONOMOUS_PARKING_001")
                .occurTime(DatetimeUtil.convertDatetimeStr2Date("2025-07-19 14:39:29"))
                .vin("LA71AUB10S0508648")
                .build();

        // 构建检查上下文
        ISCheckActionContext actionContext = ISCheckActionContext.builder()
                .item(itemDO)
                .vehicleRunTimeContext(vehicleContext)
                .build();

        try {
            // 执行检查
            ISCheckActionResult<ISInParkingAreaResult> result = isInParkingArea.execute(actionContext);
            
            System.out.println("车辆状态: 自动驾驶模式，速度0.0，位置(" + vehicleContext.getLng() + "," + vehicleContext.getLat() + ")");
            System.out.println("执行结果: " + JacksonUtils.to(result));
            
        } catch (Exception e) {
            System.out.println("❌ 测试执行异常: " + e.getMessage());
            e.printStackTrace();
        }
       
    }
   
}
