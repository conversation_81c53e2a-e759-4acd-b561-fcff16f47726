package com.sankuai.wallemonitor.risk.center.server.test.runwithspring;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.walleeve.utils.JacksonUtils;
import com.sankuai.wallemonitor.risk.center.domain.process.RiskCaseAggregateAlertProcess;
import com.sankuai.wallemonitor.risk.center.domain.service.RiskAggregateAlertService;
import com.sankuai.wallemonitor.risk.center.infra.dto.CommonEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventChangeDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventEntryDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.DomainEventExtInfoDTO;
import com.sankuai.wallemonitor.risk.center.infra.dto.LocationInfo;
import com.sankuai.wallemonitor.risk.center.infra.enums.CoordinateSystemEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.OperateEnterActionEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseSourceEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseStatusEnum;
import com.sankuai.wallemonitor.risk.center.infra.enums.RiskCaseTypeEnum;
import com.sankuai.wallemonitor.risk.center.infra.factory.DomainEventFactory;
import com.sankuai.wallemonitor.risk.center.infra.model.common.PositionDO;
import com.sankuai.wallemonitor.risk.center.infra.model.common.RiskCaseExtInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.model.core.RiskCaseDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.UserInfoRepository;
import com.sankuai.wallemonitor.risk.center.server.consumer.CommonCaseConsumer;
import com.sankuai.wallemonitor.risk.center.server.test.runwithspring.base.SpringTestBase;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * 测试从消息接收到聚合告警的完整流程
 */

public class ElephantCardTest extends SpringTestBase {

    @Resource
    private CommonCaseConsumer commonCaseConsumer;

    @Resource
    private RiskAggregateAlertService riskAggregateAlertService;

    @Resource
    private RiskCaseAggregateAlertProcess riskCaseAggregateAlertProcess;

    @Resource
    private UserInfoRepository userInfoRepository;

    /**
     * 测试CommonCaseConsumer.receive方法
     * 测试数据：caseType=101, vin=LA71AUB14S0508684
     */
    @Test
    public void testCommonCaseConsumerReceive() {
        // 1. 构建测试消息
        CommonEventDTO commonEventDTO = buildTestCommonEventDTO();
        String message = JacksonUtils.to(commonEventDTO);
        
        System.out.println("=== 开始测试CommonCaseConsumer.receive ===");
        System.out.println("测试消息内容: " + message);
        System.out.println("测试VIN: " + commonEventDTO.getVin());
        System.out.println("测试caseType: " + commonEventDTO.getCaseType());
        
        try {
            // 2. 调用receive方法
            ConsumeStatus result = commonCaseConsumer.receive(message);
            
            System.out.println("消息处理结果: " + result);
           
            
            System.out.println("=== CommonCaseConsumer.receive测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("测试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 测试聚合告警流程 - 直接调用process方法
     */
    @Test
    public void testRiskCaseAggregateAlertProcess() throws Exception {
        System.out.println("=== 测试聚合告警流程 ===");

        // 1. 构造测试用的RiskCaseDO
        RiskCaseDO riskCaseDO = buildTestRiskCaseDO();
        System.out.println("构造的风险案例:");
        System.out.println("- CaseId: " + riskCaseDO.getCaseId());
        System.out.println("- VIN: LA71AUB14S0508684");
        System.out.println("- 事件类型: " + riskCaseDO.getType());
        System.out.println("- 状态: " + riskCaseDO.getStatus());
        System.out.println("- 位置: " + riskCaseDO.getPoiName());

        // 2. 构造DomainEventChangeDTO
        DomainEventChangeDTO<RiskCaseDO> eventChangeDTO = buildDomainEventChangeDTO(riskCaseDO);
        System.out.println("构造的领域事件:");
        System.out.println("- TraceId: " + eventChangeDTO.getTraceId());
        System.out.println("- 操作类型: " + eventChangeDTO.getEntry().getOperateEntry());
        System.out.println("- 领域类: " + eventChangeDTO.getDomainClass().getSimpleName());

        // // 查询进场安全员  LA71AUB14S0508684
        // List<String> misList = userInfoRepository.batchGetMisByVin(Lists.newArrayList("LA71AUB14S0508684"));
        // System.out.println("进场安全员: " + misList);
        // // 查询uid
        // List<Long> uidList = userInfoRepository.batchGetUidsByMis(misList);
        // System.out.println("进场安全员uid: " + uidList);

        // // 查询姓名
        // List<String> nameList = userInfoRepository.batchGetNamesByMis(misList);
        // System.out.println("进场安全员姓名: " + nameList);

        // 3. 调用聚合告警处理器
        System.out.println("开始调用聚合告警处理器...");
        boolean result = riskCaseAggregateAlertProcess.process(eventChangeDTO);

        System.out.println("聚合告警处理结果: " + result);
        System.out.println("=== 聚合告警流程测试完成 ===");
    }

    /**
     * 构建测试用的CommonEventDTO
     * caseType=101, vin=LA71AUB14S0508684
     */
    private CommonEventDTO buildTestCommonEventDTO() {
        // 构建位置信息（GCJ02坐标系）
        LocationInfo location = LocationInfo.builder()
                .latitude("39.9042")  // 北京纬度
                .longitude("116.4074") // 北京经度
                .positionSource("autocar_utm") // 高精融合定位
                .build();

        // 构建扩展信息
        Map<String, Object> extInfo = new HashMap<>();
        extInfo.put("testFlag", true);
        extInfo.put("testDescription", "聚合告警@人功能测试");
        extInfo.put("riskLevel", 3);
        extInfo.put("testVin", "LA71AUB14S0508684");

        // 构建CommonEventDTO
        CommonEventDTO dto = CommonEventDTO.builder()
                .vin("LA71AUB14S0508684")  // 统一使用测试VIN
                .eventId("test_event_" + System.currentTimeMillis())  // 事件唯一ID
                .caseType(101)  // 指定事件类型为101
                .occurTime(System.currentTimeMillis())  // 事件发生时间
                .source(1)  // 事件来源：1-保障系统
                .location(location)  // 位置信息
                .extInfo(extInfo)  // 扩展字段
                .description("测试聚合告警流程 - VIN:LA71AUB14S0508684, caseType:101")  // 事件描述
                .build();
        
        return dto;
    }

    /**
     * 构建测试用的RiskCaseDO
     * 基于数据库中的测试数据：VIN=LA71AUB14S0508684, caseType=101
     */
    private RiskCaseDO buildTestRiskCaseDO() {
        // 构建位置信息
        PositionDO position = PositionDO.builder()
                .latitude(39.9042)
                .longitude(116.4074)
                .coordinateSystem(CoordinateSystemEnum.GCJ02)
                .build();

        // 构建扩展信息
        RiskCaseExtInfoDO extInfo = RiskCaseExtInfoDO.builder()
                .city("北京市")
                .are("东城区")
                .poi("北京市人民政府(旧址)")
                .position(position)
                .eventDesc("测试聚合告警流程 - VIN:LA71AUB14S0508684, caseType:101")
                .build();

        // 构建RiskCaseDO
        RiskCaseDO riskCaseDO = RiskCaseDO.builder()
                .caseId("h******20250716150248S07T101")  // 使用数据库中的caseId
                .eventId("test_event_" + System.currentTimeMillis())
                .type(RiskCaseTypeEnum.AUTOMATIC_START_EVENT)  // caseType=101
                .status(RiskCaseStatusEnum.NO_DISPOSAL)  // 状态：待处置，这样会触发聚合告警
                .source(RiskCaseSourceEnum.BEACON_TOWER)  // 来源：保障系统
                .recallTime(new Date())
                .occurTime(new Date())
                .createTime(new Date())
                .updateTime(new Date())
                .extInfo(extInfo)
                .poiName("北京市人民政府(旧址)")
                .level(10)
                .build();

        return riskCaseDO;
    }

    /**
     * 构建DomainEventChangeDTO
     */
    private DomainEventChangeDTO<RiskCaseDO> buildDomainEventChangeDTO(RiskCaseDO riskCaseDO) {
        // 1. 构建DomainEventDTO
        DomainEventDTO<RiskCaseDO> domainEventDTO = DomainEventDTO.<RiskCaseDO>builder()
                .before(new ArrayList<>())  // 新增事件，before为空
                .after(Arrays.asList(riskCaseDO))  // after包含新的风险案例
                .entry(DomainEventEntryDTO.builder()
                        .domainClassName(RiskCaseDO.class.getSimpleName())
                        .operateEntry(OperateEnterActionEnum.RISK_EVENT_CONSUMER_ENTER)
                        .build())
                .timestamp(System.currentTimeMillis())
                .operator("test_user")
                .traceId("test_trace_" + System.currentTimeMillis())
                .extInfo(DomainEventExtInfoDTO.builder().build())
                .build();

        // 2. 使用工厂方法创建DomainEventChangeDTO
        return DomainEventFactory.createDomainEventChangeDTO(domainEventDTO, RiskCaseDO.class);
    }
   
}