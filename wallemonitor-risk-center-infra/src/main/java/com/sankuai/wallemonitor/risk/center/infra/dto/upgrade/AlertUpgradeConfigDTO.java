package com.sankuai.wallemonitor.risk.center.infra.dto.upgrade;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 风险告警升级配置DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlertUpgradeConfigDTO {

    /**
     * 升级功能总开关
     */
    @Builder.Default
    private Boolean enabled = false;

    /**
     * 升级策略列表
     */
    private List<AlertUpgradeStrategyDTO> strategies;

    /**
     * 校验配置是否有效
     */
    public boolean isValid() {
        return enabled != null && enabled && CollectionUtils.isNotEmpty(strategies);
    }

    /**
     * 获取有效的策略列表
     */
    public List<AlertUpgradeStrategyDTO> getValidStrategies() {
        if (CollectionUtils.isEmpty(strategies)) {
            return new ArrayList<>();
        }
        return strategies.stream()
                .filter(AlertUpgradeStrategyDTO::isValid)
                .collect(Collectors.toList());
    }
  
}
