package com.sankuai.wallemonitor.risk.center.infra.dto;

import com.sankuai.wallemonitor.risk.center.infra.model.common.HdMapElementGeoDO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PreDataContextDTO {

    @Builder.Default
    private List<HdMapElementGeoDO> nearbyLaneList = new ArrayList<>();
    @Builder.Default
    private Map<String, List<HdMapElementGeoDO>> vehicleAroundLane = new HashMap<>();
    private HdMapElementGeoDO vehicleCurLane;
}
