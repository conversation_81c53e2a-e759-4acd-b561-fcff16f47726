package com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo;
import java.util.List;

public interface UserInfoRepository {
    /**
     * 批量根据VIN获取近场安全员
     *
     * @param vinList VIN列表
     * @return 去重后的近场安全员列表
     */
    List<String> batchGetSubstituteByVin(List<String> vinList);

    /**
     * 批量根据MIS获取用户姓名
     *
     * @param misList MIS账号列表
     * @return 姓名列表（与misList顺序对应）
     */
    List<String> batchGetNamesByMis(List<String> misList);

    /**
     * 批量根据MIS获取UID
     *
     * @param misList MIS账号列表
     * @return UID列表（与misList顺序对应）
     */
    List<Long> batchGetUidsByMis(List<String> misList);

}

