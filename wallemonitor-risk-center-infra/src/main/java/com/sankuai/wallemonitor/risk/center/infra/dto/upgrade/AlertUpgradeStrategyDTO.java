package com.sankuai.wallemonitor.risk.center.infra.dto.upgrade;

import com.sankuai.wallemonitor.risk.center.infra.dto.aggregate.AlertTemplateConfigDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 风险告警升级策略配置DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlertUpgradeStrategyDTO {

    /**
     * 告警策略
     * 例如: "single_case"
     */
    private String alertPolicy;

    /**
     * 配置名称，用于记录最终告警由哪个配置产生
     */
    private String configName;

    /**
     * 事件类型 - 指定该升级配置针对特定类型的事件
     */
    private Integer caseType;

    /**
     * 是否启用
     */
    private Boolean enabledStrategy;


    /**
     * 告警模板配置
     */
    private AlertTemplateConfigDTO alertTemplate;

    /**
     * 校验配置是否有效
     */
    public boolean isValid() {
        return StringUtils.isNotBlank(alertPolicy)
                && StringUtils.isNotBlank(configName)
                && alertTemplate != null && alertTemplate.isValid()
                && enabledStrategy != null && enabledStrategy;
    }

}
