package com.sankuai.wallemonitor.risk.center.infra.dto.lion;

import com.sankuai.wallemonitor.risk.center.infra.dto.SeatInterventionConfigDTO;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class DetectWaitQueueConfigDTO {

    /**
     * 车道搜索范围距离（米）
     */
    private Double range;

    /**
     * 使用历史位置的前移秒数
     */
    private Integer pastSecond;

    /**
     * 根据obstacleType判断的道路中心线夹角阈值
     */
    private Map<String, Double> angleThresholdByObstacleType;

    /**
     * 默认障碍物检测距离阈值（米）
     */
    private Double defaultDistanceThreshold;

    /**
     * 障碍物类型距离阈值（米）
     */
    private Map<String, Double> fineTypeDistanceThreshold;


    /**
     * 区域
     */
    private List<String> laneTypeList;

    /**
     * 障碍物的类型
     */
    private List<String> fineTypeList;

    /**
     * 是否使用中心线做夹角判定
     */
    private Boolean useMiddleLineAngle;

    /**
     * 车辆前序定位点需要满足的距离要求（防止过近导致的异常）
     */
    private Double preMinDistance;

    /**
     * 开启可通行车道检测的阈值
     */
    private Double thresholdOpenUsableLaneChecking;

    /**
     * 可通行车道检测的障碍物夹角阈值
     */
    private Double usableLaneObstacleAngleThreshold;

    /**
     * 可通行车道检测的障碍物距离阈值
     */
    private Double usableLaneObstacleDistanceThreshold;

    /**
     * 开启非机动车道路口检测的阈值
     */
    private Double thresholdOpenBikingLaneChecking;

    /**
     * 开启非机动车道路口检测的阈值
     */
    private List<String> sensitiveLaneTypeList;


    /**
     * 后方障碍物检测阈值
     */
    private BehindObstacleCheckConfig behindObstacleCheckConfig;

    /**
     * 自车传递障碍物检测
     */
    private ExtendObstacleCheckConfig extendObstacleCheckConfig;

    /**
     * 自车压线排队检测配置
     */
    private CrossLineWaitCheckConfig crossLineWaitCheckConfig;

    /**
     * 自车前后换帧检查配置
     */
    private SwitchObstacleCheckConfig switchObstacleWaitCheckConfig;

    /**
     * 是否使用FC计算可通行车道
     */
    private Boolean enableUsableLaneFromFc;

    /**
     * 路口等待配置
     */
    private JunctionWaitConfigDTO junctionWaitConfig;
    /**
     * 最右车道或次右车道无灯排队
     */
    private NonTrafficLightWaitInLineDTO nonTrafficLightWaitConfig;

    /**
     * 距离最近车道线的角度阈值，用于排除不满足角度要求的线段参与最近距离计算
     */
    @Default
    private Double distance2NearByAngle = 15D;

    /**
     * 同向车道的判定夹角
     */
    @Default
    private Double laneSameDirectionTheta = 30D;

    /**
     * 前方同距车辆检测配置
     */
    private MultiCarInSameDistanceConfigDTO multiCarInSameDistanceConfig;

    /**
     * 坐席介入检测配置
     */
    private SeatInterventionConfigDTO seatInterventionConfig;

}
