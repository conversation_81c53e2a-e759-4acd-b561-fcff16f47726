package com.sankuai.wallemonitor.risk.center.infra.dto;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 堵路事件发送DTO
 * 用于发送堵路事件相关信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TrafficBlockEventSendDTO {

    /**
     * 风险案件ID
     */
    private String caseId;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 事件发生时间，格式：yyyy-MM-dd HH:mm:ss
     */
    private String occurTime;

    /**
     * 事件解除时间，格式：yyyy-MM-dd HH:mm:ss
     */
    private String closeTime;

    /**
     * 纬度(GCJ02)
     */
    private Double latitude;

    /**
     * 经度(GCJ02)
     */
    private Double longitude;

    /**
     * 位置信息
     */
    private Position position;

    /**
     * 上报者信息
     */
    private ReporterInfo reporterInfo;

    /**
     * 扩展信息
     */
    private Map<String, Object> extInfo;

    /**
     * 位置信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Position {

        /**
         * X坐标
         */
        private Double x;

        /**
         * Y坐标
         */
        private Double y;
    }

    /**
     * 上报者信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ReporterInfo {

        /**
         * 坐席ID
         */
        private String cockpitId;

        /**
         * 坐席名称
         */
        private String cockpitName;

        /**
         * 上报人
         */
        private String reporter;
    }


    /**
     * 从 TrafficBlockEventMessageDTO.Position 转换
     */
    public static Position fromMessagePosition(TrafficBlockEventMessageDTO.Position messagePosition) {
        if (messagePosition == null) {
            return null;
        }
        return Position.builder()
                .x(messagePosition.getX())
                .y(messagePosition.getY())
                .build();
    }

    /**
     * 从 TrafficBlockEventMessageDTO.ReporterInfo 转换
     */
    public static ReporterInfo fromMessageReporterInfo(TrafficBlockEventMessageDTO.ReporterInfo messageReporterInfo) {
        if (messageReporterInfo == null) {
            return null;
        }
        return ReporterInfo.builder()
                .cockpitId(messageReporterInfo.getCockpitId())
                .cockpitName(messageReporterInfo.getCockpitName())
                .reporter(messageReporterInfo.getReporter())
                .build();
    }

}