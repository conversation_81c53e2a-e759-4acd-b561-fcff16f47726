package com.sankuai.wallemonitor.risk.center.infra.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 云控坐席操作事件消息DTO 用于处理来自云控后端的坐席操作事件，如区域适配事件上报等
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TrafficBlockEventMessageDTO {

        /**
         * 操作类型 例如: mender_event_report - 区域适配事件上报
         */
        private String operationType;

        /**
         * 业务数据
         */
        private CockpitOperationData data;

        /**
         * 云控坐席操作数据
         */
        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class CockpitOperationData {

                /**
                 * 车架号
                 */
                private String vin;

                /**
                 * 地图名称
                 */
                private String mapName;

                /**
                 * 位置信息
                 */
                private Position position;

                /**
                 * 上报者信息
                 */
                private ReporterInfo reporterInfo;

                /**
                 * 来源
                 */
                private String source;

                /**
                 * 触发时间
                 */
                private String triggerTime;

                /**
                 * 事件类型
                 */
        private String type;
        }

        /**
         * 位置信息
         */
        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class Position {

                /**
                 * X坐标
                 */
                private Double x;

                /**
                 * Y坐标
                 */
                private Double y;
        }

        /**
         * 上报者信息
         */
        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class ReporterInfo {

                /**
                 * 坐席ID
                 */
                private String cockpitId;

                /**
                 * 坐席名称
                 */
                private String cockpitName;

                /**
                 * 上报人
                 */
                private String reporter;
        }
}
