package com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.impl;
import com.dianping.cat.Cat;
import com.sankuai.meituan.org.opensdk.model.domain.Emp;
import com.sankuai.meituan.org.opensdk.service.EmpService;
import com.sankuai.wallemonitor.risk.center.infra.model.core.VehicleInfoDO;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.UserInfoRepository;
import com.sankuai.wallemonitor.risk.center.infra.repository.adapterrepo.VehicleInfoRepository;
import com.sankuai.xm.udb.thrift.UdbOpenThriftClient;

import java.util.ArrayList;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


/**
 * 用户信息服务实现类
 */
@Service
@Slf4j
public class UserInfoRepositoryImpl implements UserInfoRepository {
    @Resource
    private VehicleInfoRepository vehicleInfoRepository;

    @Resource
    private EmpService empService;

    @Resource
    private UdbOpenThriftClient udbOpenThriftClient;

    // UDB租户配置，需要根据实际配置调整
    @Value("${udb.tenant}")
    private String udbTenant;

    /**
     * 批量根据VIN获取近场安全员
     *
     * @param vinList VIN列表
     * @return 去重后的近场安全员列表
     */
    @Override
    public List<String> batchGetSubstituteByVin(List<String> vinList) {
        if (CollectionUtils.isEmpty(vinList)) {
            return new ArrayList<>();
        }

        try {
            // 查询车辆信息
            List<VehicleInfoDO> vehicleInfoList = vehicleInfoRepository.queryByVinList(vinList);

            List<String> misList = new ArrayList<>();
            for (VehicleInfoDO vehicleInfo : vehicleInfoList) {
                // 添加近场安全员
                if (StringUtils.isNotBlank(vehicleInfo.getSubstitute())) {
                    misList.add(vehicleInfo.getSubstitute());
                }
            }
            // 去重
            return misList.stream().distinct().collect(Collectors.toList());

        } catch (Exception e) {
            log.error("根据VIN列表获取MIS列表失败: vinList={}", vinList, e);
            return new ArrayList<>();
        }
    }

    /**
     * 批量根据MIS获取用户姓名
     *
     * @param misList MIS账号列表
     * @return 姓名列表（与misList顺序对应）
     */
    @Override
    public List<String> batchGetNamesByMis(List<String> misList) {
        List<String> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(misList)) {
            return result;
        }

        try {
            List<Emp> empList = batchQueryByMis(misList);
           
            for (Emp emp : empList) {
                if (emp != null && StringUtils.isNotBlank(emp.getName())) {
                    String empName = emp.getName();
                    result.add(empName);
                }
            }

        } catch (Exception e) {
            log.error("批量获取用户姓名失败: misList={}", misList, e);
            return new ArrayList<>();
        }

        return result.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 批量根据MIS获取UID
     *
     * @param misList MIS账号列表
     * @return UID列表（与misList顺序对应）
     */
    @Override
    public List<Long> batchGetUidsByMis(List<String> misList) {
        List<Long> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(misList)) {
            return result;
        }

        try {
            Map<String, Long> uidMap = udbOpenThriftClient.getUids(misList, udbTenant);

            // 按照输入顺序返回UID列表
            for (String mis : misList) {
                Long uid = uidMap.get(mis);
                result.add(uid); // 可能为null
            }

        } catch (Exception e) {
            log.error("批量获取UID失败: misList={}", misList, e);
            return new ArrayList<>();
        }

        return result.stream().distinct().collect(Collectors.toList());
    }



    /**
     * 批量根据MIS查询员工信息
     */
    private List<Emp> batchQueryByMis(List<String> batchList) throws InterruptedException {
        int retryNum = 3;
        while (retryNum > 0) {
            try {
                log.info("batchQueryByMis is run , retryNum = {}", retryNum);
                return empService.batchQueryByMis(batchList, null);
            } catch (Exception e) {
                log.error("batchQueryByMis is error", e);
                Cat.logError(e);
                retryNum--;
                if (retryNum > 0) {
                    delayMilliseconds(1000);
                }
            }
        }
        return new ArrayList<>();
    }

    /**
     * 根据MIS列表获取UID列表
     */
    public List<Long> getUids(List<String> misList) {
        List<Long> uids = new ArrayList<>();
        try {
            Map<String, Long> map = udbOpenThriftClient.getUids(misList, udbTenant);
            for (Long uid : map.values()) {
                uids.add(uid);
            }
        } catch (Exception e) {
            log.error("获取UID失败", e);
        }
        log.info("get uids: {}", uids);
        return uids;
    }

    /**
     * 延迟方法
     */
    private void delayMilliseconds(long milliseconds) throws InterruptedException {
        Thread.sleep(milliseconds);
    }

}






