package com.sankuai.wallemonitor.risk.center.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 升级状态枚举
 */
@Getter
@AllArgsConstructor
public enum UpgradeStatusEnum {

    /**
     * 无需升级
     */
    NO_NEED_UPGRADE(0, "无需升级"),

    /**
     * 需要升级
     */
    NEED_UPGRADE(1, "需要升级"),

    /**
     * 完成升级
     */
    UPGRADED(2, "完成升级");

    private final Integer code;
    private final String desc;

    /**
     * 根据code查找枚举
     */
    public static UpgradeStatusEnum findByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (UpgradeStatusEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 是否无需升级
     */
    public boolean isNoNeedUpgrade() {
        return this == NO_NEED_UPGRADE;
    }

    /**
     * 是否需要升级
     */
    public boolean isNeedUpgrade() {
        return this == NEED_UPGRADE;
    }

    /**
     * 是否已完成升级
     */
    public boolean isUpgraded() {
        return this == UPGRADED;
    }

    /**
     * 是否可以进行升级（状态为需要升级）
     */
    public boolean canUpgrade() {
        return this == NEED_UPGRADE;
    }

}
